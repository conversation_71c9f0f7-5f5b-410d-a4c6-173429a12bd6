/**
 * API endpoint para upload de imagens para Chevereto
 *
 * Este endpoint recebe uma URL de imagem e faz upload para o serviço Chevereto
 * configurado, retornando a URL da imagem hospedada.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import FormData from 'form-data';
import https from 'https';
import { URL } from 'url';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Apenas aceitar requisições POST
  if (req.method !== 'POST') {
    return res.status(405).json({
      error: 'Método não permitido',
      message: 'Este endpoint aceita apenas requisições POST'
    });
  }

  // Validar se a chave da API está configurada
  if (!process.env.CHEVERETO_API) {
    console.error('CHEVERETO_API não está configurada no ambiente');
    return res.status(500).json({
      error: 'Configuração do servidor',
      message: 'Serviço de upload não está configurado corretamente'
    });
  }

  const { imageUrl } = req.body;

  // Validar parâmetros da requisição
  if (!imageUrl) {
    return res.status(400).json({
      error: 'Parâmetro obrigatório',
      message: 'URL da imagem é obrigatória'
    });
  }

  // Validar se é uma URL válida
  try {
    new URL(imageUrl);
  } catch (error) {
    return res.status(400).json({
      error: 'URL inválida',
      message: 'A URL fornecida não é válida'
    });
  }

  // Validar se é uma URL de imagem do Google (por segurança)
  if (!imageUrl.includes('googleapis.com')) {
    return res.status(400).json({
      error: 'URL não permitida',
      message: 'Apenas URLs de imagens do Google Places são permitidas'
    });
  }

  try {
    // Primeiro, fazer download da imagem
    console.log('Fazendo download da imagem:', imageUrl);
    const imageResponse = await fetch(imageUrl);

    if (!imageResponse.ok) {
      console.error('Erro ao fazer download da imagem:', imageResponse.status);
      return res.status(400).json({
        error: 'Erro no download',
        message: 'Não foi possível fazer download da imagem do Google Places'
      });
    }

    // Obter o buffer da imagem
    const imageBuffer = await imageResponse.arrayBuffer();
    const buffer = Buffer.from(imageBuffer);

    // Determinar extensão baseada no content-type
    const contentType = imageResponse.headers.get('content-type') || 'image/jpeg';
    const extension = contentType.includes('png') ? 'png' : 'jpg';
    const filename = `gplaces-img.${extension}`;

    // Preparar dados para o Chevereto usando form-data
    const formData = new FormData();
    formData.append('source', buffer, {
      filename: filename,
      contentType: contentType
    });
    formData.append('format', 'json');

    console.log('Enviando para Chevereto, tamanho:', buffer.length, 'bytes');

    // Fazer requisição para a API do Chevereto usando https module
    const cheveretoResponse = await new Promise<{
      ok: boolean;
      status: number;
      text: () => Promise<string>;
      json: () => Promise<any>;
    }>((resolve, reject) => {
      const url = new URL('https://db.avenca.cloud/api/1/upload');

      const options = {
        hostname: url.hostname,
        port: url.port || 443,
        path: url.pathname,
        method: 'POST',
        headers: {
          'X-API-Key': process.env.CHEVERETO_API,
          ...formData.getHeaders(),
        },
      };

      const req = https.request(options, (res) => {
        let data = '';

        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          resolve({
            ok: (res.statusCode ?? 500) >= 200 && (res.statusCode ?? 500) < 300,
            status: res.statusCode ?? 500,
            text: async () => data,
            json: async () => JSON.parse(data),
          });
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      formData.pipe(req);
    });

    // Verificar se a resposta foi bem-sucedida
    if (!cheveretoResponse.ok) {
      const errorText = await cheveretoResponse.text();
      console.error('Erro na API do Chevereto:', cheveretoResponse.status, errorText);

      // Tratar diferentes tipos de erro HTTP
      switch (cheveretoResponse.status) {
        case 401:
          return res.status(500).json({
            error: 'Erro de autenticação',
            message: 'Chave da API inválida ou expirada'
          });
        case 403:
          return res.status(500).json({
            error: 'Acesso negado',
            message: 'Sem permissão para fazer upload'
          });
        case 413:
          return res.status(400).json({
            error: 'Arquivo muito grande',
            message: 'A imagem excede o tamanho máximo permitido'
          });
        case 415:
          return res.status(400).json({
            error: 'Formato não suportado',
            message: 'Formato de imagem não é suportado'
          });
        case 429:
          return res.status(429).json({
            error: 'Muitas requisições',
            message: 'Limite de uploads excedido. Tente novamente em alguns minutos'
          });
        case 503:
          return res.status(503).json({
            error: 'Serviço indisponível',
            message: 'Servidor de upload temporariamente indisponível'
          });
        default:
          return res.status(500).json({
            error: 'Erro no servidor',
            message: 'Erro interno no serviço de upload'
          });
      }
    }

    // Processar resposta JSON
    const cheveretoData = await cheveretoResponse.json();

    // Verificar se o upload foi bem-sucedido
    if (cheveretoData.status_code !== 200 || !cheveretoData.success) {
      console.error('Erro no upload do Chevereto:', cheveretoData);
      return res.status(400).json({
        error: 'Falha no upload',
        message: cheveretoData.error?.message || 'Não foi possível fazer upload da imagem'
      });
    }

    // Extrair URL da imagem uploadada
    const uploadedImageUrl = cheveretoData.image?.url;
    if (!uploadedImageUrl) {
      console.error('URL da imagem não encontrada na resposta:', cheveretoData);
      return res.status(500).json({
        error: 'Resposta inválida',
        message: 'URL da imagem não foi retornada pelo serviço'
      });
    }

    // Retornar sucesso com dados da imagem
    return res.status(200).json({
      success: true,
      message: 'Imagem enviada com sucesso',
      data: {
        url: uploadedImageUrl,
        viewerUrl: cheveretoData.image?.url_viewer,
        filename: cheveretoData.image?.filename,
        size: cheveretoData.image?.size_formatted,
        uploadDate: cheveretoData.image?.date,
      }
    });

  } catch (error) {
    console.error('Erro ao fazer upload para Chevereto:', error);

    // Tratar diferentes tipos de erro
    if (error instanceof Error) {
      if ((error as NodeJS.ErrnoException).code === 'ECONNREFUSED') {
        return res.status(503).json({
          error: 'Erro de conectividade',
          message: 'Não foi possível conectar ao serviço de upload. Tente novamente em alguns instantes'
        });
      }

      if ((error as NodeJS.ErrnoException).code === 'ETIMEDOUT') {
        return res.status(408).json({
          error: 'Timeout',
          message: 'Upload demorou muito para completar. Tente novamente'
        });
      }
    }

    return res.status(500).json({
      error: 'Erro interno',
      message: 'Erro inesperado durante o upload. Tente novamente'
    });
  }
}
